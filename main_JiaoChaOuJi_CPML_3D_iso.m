% Parameters
% Grid dimensions
NX = 100;
NY = 100;
NZ = 400;


% Grid spacing
DELTAX = 0.02;
DELTAY = DELTAX;
DELTAZ = DELTAX;
ONE_OVER_DELTAX = 1.0 / DELTAX;
ONE_OVER_DELTAY = ONE_OVER_DELTAX;
ONE_OVER_DELTAZ = ONE_OVER_DELTAX;


% ����Բ���İ뾶
radius = 0.10; % Բ���뾶Ϊ0.1m

% ����Բ����������ĵ�
center_x = ((NX)/2) * DELTAX; % ����������x����
center_y = ((NY)/2) * DELTAX; % ����������y����


    % P-velocity, S-velocity and density
 vf_p=1500;
 vf_s=0;
 rho_f=1000;
 cp = 4000;
 cs = 2300;
 rho = 2500;
    
 VP = cp * ones(NX, NY, NZ);
 VS = cs * ones(NX, NY, NZ);
 RHO = rho * ones(NX, NY, NZ);

 
for i = 1:NX
    for j = 1:NY
        for k = 1:NZ
            % ���㵱ǰ���x��y����
            x = i * DELTAX;
            y = j * DELTAX;
            
            % �жϵ�ǰ���Ƿ���Բ��������
            if sqrt((x - center_x)^2 + (y - center_y)^2) <= radius
                VP(i, j, k) = vf_p;
                VS(i, j, k) = vf_s;
                RHO(i, j, k) = rho_f;
            end
        end
    end
end

mu = RHO .* VS.^2;
lambda = RHO.* (VP.^2 - 2.0 * VS.^2);
lambdaplustwomu = RHO .* VP.^2; 
 
 
 % RHO ������ƽ��
RHO_x_avg = RHO;  % x ���������ƽ��
RHO_y_avg = RHO;  % y ���������ƽ��
RHO_z_avg = RHO;  % z ���������ƽ��

RHO_x_avg(1:NX-1,:,:)=(RHO(1:NX-1,:,:)+RHO(2:NX,:,:))/2;
RHO_y_avg(:,1:NY-1,:)=(RHO(:,1:NY-1,:)+RHO(:,2:NY,:))/2;
RHO_z_avg(:,:,1:NZ-1)=(RHO(:,:,1:NZ-1)+RHO(:,:,2:NZ))/2;

mu_xy_avg = mu; % x-y ƽ��ĵ���ƽ��
mu_xz_avg = mu; % x-z ƽ��ĵ���ƽ��
mu_yz_avg = mu; % y-z ƽ��ĵ���ƽ��


for i = 2:NX
    for j = 1:NY-1
        for k = 1:NZ
            if all(mu(i-1:i, j:j+1, k) ~= 0)
                mu_xy_avg(i,j,k)=4/(1/mu(i,j,k)+1/mu(i,j+1,k)+1/mu(i-1,j,k)+1/mu(i-1,j+1,k));
            else
                mu_xy_avg(i, j, k) = 0;
            end
        end
    end
end

for i = 2:NX
    for j = 1:NY
        for k = 1:NZ-1
            if all(mu(i-1:i, j, k:k+1) ~= 0)
                mu_xz_avg(i,j,k)=4/(1/mu(i,j,k+1)+1/mu(i,j,k)+1/mu(i-1,j,k+1)+1/mu(i-1,j,k));
            else
                mu_xz_avg(i, j, k) = 0;
            end
        end
    end
end

for i = 1:NX
    for j = 1:NY-1
        for k = 1:NZ-1
            if all(mu(i, j:j+1, k:k+1) ~= 0)
                mu_yz_avg(i, j, k)=4/(1/mu(i,j,k+1)+1/mu(i,j,k)+1/mu(i,j+1,k+1)+1/mu(i,j+1,k));
            else
                mu_yz_avg(i, j, k) = 0;
            end
        end
    end
end





% Time parameters
NSTEP = 4000;
DELTAT = 2e-6;

% Source parameters
f0 = 3000.0;
t0 = 1.20 / f0;
factor = 1e7;

% PML parameters
USE_PML_XMIN = true;
USE_PML_XMAX = true;
USE_PML_YMIN = true;
USE_PML_YMAX = true;
USE_PML_ZMIN = true;
USE_PML_ZMAX = true;
NPOINTS_PML = 20;

% Source location
ISOURCE = round(NX / 2) ;
JSOURCE = round(NY / 2) ;
KSOURCE = round(NZ / 4);  
xsource = (ISOURCE - 1) * DELTAX;
ysource = (JSOURCE - 1) * DELTAY;


% Constants
PI = 3.141592653589793;
DEGREES_TO_RADIANS = PI / 180.0;
ZERO = 0.0;
NPOWER = 2.0;
K_MAX_PML = 1.0;
ALPHA_MAX_PML = 2.0*PI*(f0/2.0);

% Precomputed constants
DELTAT_lambda = DELTAT * lambda;
DELTAT_mu_xy_avg = DELTAT * mu_xy_avg;
DELTAT_mu_xz_avg = DELTAT * mu_xz_avg;
DELTAT_mu_yz_avg = DELTAT * mu_yz_avg;
DELTAT_lambdaplus2mu = DELTAT * lambdaplustwomu;
DELTAT_over_rhox = DELTAT / RHO_x_avg;
DELTAT_over_rhoy = DELTAT / RHO_y_avg;
DELTAT_over_rhoz = DELTAT / RHO_z_avg;
%% Initialize arrays
% Main field arrays
vx = zeros(NX, NY, NZ);
vy = zeros(NX, NY, NZ);
vz = zeros(NX, NY, NZ);
sigmaxx = zeros(NX, NY, NZ);
sigmayy = zeros(NX, NY, NZ);
sigmazz = zeros(NX, NY, NZ);
sigmaxy = zeros(NX, NY, NZ);
sigmaxz = zeros(NX, NY, NZ);
sigmayz = zeros(NX, NY, NZ);

% PML memory variables
memory_dvx_dx = zeros(NX, NY, NZ);
memory_dvx_dy = zeros(NX, NY, NZ);
memory_dvx_dz = zeros(NX, NY, NZ);
memory_dvy_dx = zeros(NX, NY, NZ);
memory_dvy_dy = zeros(NX, NY, NZ);
memory_dvy_dz = zeros(NX, NY, NZ);
memory_dvz_dx = zeros(NX, NY, NZ);
memory_dvz_dy = zeros(NX, NY, NZ);
memory_dvz_dz = zeros(NX, NY, NZ);
memory_dsigmaxx_dx = zeros(NX, NY, NZ);
memory_dsigmayy_dy = zeros(NX, NY, NZ);
memory_dsigmazz_dz = zeros(NX, NY, NZ);
memory_dsigmaxy_dx = zeros(NX, NY, NZ);
memory_dsigmaxy_dy = zeros(NX, NY, NZ);
memory_dsigmaxz_dx = zeros(NX, NY, NZ);
memory_dsigmaxz_dz = zeros(NX, NY, NZ);
memory_dsigmayz_dy = zeros(NX, NY, NZ);
memory_dsigmayz_dz = zeros(NX, NY, NZ);

%% Setup PML profiles
thickness_PML_x = NPOINTS_PML * DELTAX;
thickness_PML_y = NPOINTS_PML * DELTAY;
thickness_PML_z = NPOINTS_PML * DELTAZ;

Rcoef = 0.001;
d0_x = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_x);
d0_y = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_y);
d0_z = -(NPOWER + 1) * cp * log(Rcoef) / (2.0 * thickness_PML_z);

% Initialize damping profiles
d_x = zeros(NX, 1);
d_x_half = zeros(NX, 1);
K_x = ones(NX, 1);
K_x_half = ones(NX, 1);
alpha_x = zeros(NX, 1);
alpha_x_half = zeros(NX, 1);
a_x = zeros(NX, 1);
a_x_half = zeros(NX, 1);
b_x = ones(NX, 1);  % Initialize to 1 instead of 0
b_x_half = ones(NX, 1);  % Initialize to 1 instead of 0

d_y = zeros(NY, 1);
d_y_half = zeros(NY, 1);
K_y = ones(NY, 1);
K_y_half = ones(NY, 1);
alpha_y = zeros(NY, 1);
alpha_y_half = zeros(NY, 1);
a_y = zeros(NY, 1);
a_y_half = zeros(NY, 1);
b_y = ones(NY, 1);  % Initialize to 1 instead of 0
b_y_half = ones(NY, 1);  % Initialize to 1 instead of 0

d_z = zeros(NZ, 1);
d_z_half = zeros(NZ, 1);
K_z = ones(NZ, 1);
K_z_half = ones(NZ, 1);
alpha_z = zeros(NZ, 1);
alpha_z_half = zeros(NZ, 1);
a_z = zeros(NZ, 1);
a_z_half = zeros(NZ, 1);
b_z = ones(NZ, 1);  % Initialize to 1 instead of 0
b_z_half = ones(NZ, 1);  % Initialize to 1 instead of 0

% Compute damping profiles for X direction
xoriginleft = thickness_PML_x;
xoriginright = (NX-1)*DELTAX - thickness_PML_x;

for i = 1:NX
    xval = DELTAX * (i-1);
    
    % xmin edge
    if USE_PML_XMIN
        abscissa_in_PML = xoriginleft - xval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x(i) = d0_x * abscissa_normalized^NPOWER;
            K_x(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = xoriginleft - (xval + DELTAX/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x_half(i) = d0_x * abscissa_normalized^NPOWER;
            K_x_half(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x_half(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % xmax edge
    if USE_PML_XMAX
        abscissa_in_PML = xval - xoriginright;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x(i) = d0_x * abscissa_normalized^NPOWER;
            K_x(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = xval + DELTAX/2.0 - xoriginright;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_x;
            d_x_half(i) = d0_x * abscissa_normalized^NPOWER;
            K_x_half(i) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_x_half(i) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    alpha_x(i) = max(alpha_x(i), ZERO);
    alpha_x_half(i) = max(alpha_x_half(i), ZERO);
    
    b_x(i) = exp(-(d_x(i) / K_x(i) + alpha_x(i)) * DELTAT);
    b_x_half(i) = exp(-(d_x_half(i) / K_x_half(i) + alpha_x_half(i)) * DELTAT);
    
    if abs(d_x(i)) > 1e-6
        a_x(i) = d_x(i) * (b_x(i) - 1.0) / (K_x(i) * (d_x(i) + K_x(i) * alpha_x(i)));
    end
    if abs(d_x_half(i)) > 1e-6
        a_x_half(i) = d_x_half(i) * (b_x_half(i) - 1.0) / (K_x_half(i) * (d_x_half(i) + K_x_half(i) * alpha_x_half(i)));
    end
end

% Compute damping profiles for Y direction
yoriginbottom = thickness_PML_y;
yorigintop = (NY-1)*DELTAY - thickness_PML_y;

for j = 1:NY
    yval = DELTAY * (j-1);
    
    % ymin edge
    if USE_PML_YMIN
        abscissa_in_PML = yoriginbottom - yval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y(j) = d0_y * abscissa_normalized^NPOWER;
            K_y(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = yoriginbottom - (yval + DELTAY/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y_half(j) = d0_y * abscissa_normalized^NPOWER;
            K_y_half(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y_half(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % ymax edge
    if USE_PML_YMAX
        abscissa_in_PML = yval - yorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y(j) = d0_y * abscissa_normalized^NPOWER;
            K_y(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = yval + DELTAY/2.0 - yorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_y;
            d_y_half(j) = d0_y * abscissa_normalized^NPOWER;
            K_y_half(j) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_y_half(j) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    b_y(j) = exp(-(d_y(j) / K_y(j) + alpha_y(j)) * DELTAT);
    b_y_half(j) = exp(-(d_y_half(j) / K_y_half(j) + alpha_y_half(j)) * DELTAT);
    
    if abs(d_y(j)) > 1e-6
        a_y(j) = d_y(j) * (b_y(j) - 1.0) / (K_y(j) * (d_y(j) + K_y(j) * alpha_y(j)));
    end
    if abs(d_y_half(j)) > 1e-6
        a_y_half(j) = d_y_half(j) * (b_y_half(j) - 1.0) / (K_y_half(j) * (d_y_half(j) + K_y_half(j) * alpha_y_half(j)));
    end
end

% Compute damping profiles for Z direction
zoriginbottom = thickness_PML_z;
zorigintop = (NZ-1)*DELTAZ - thickness_PML_z;

for k = 1:NZ
    zval = DELTAZ * (k-1);
    
    % zmin edge
    if USE_PML_ZMIN
        abscissa_in_PML = zoriginbottom - zval;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z(k) = d0_z * abscissa_normalized^NPOWER;
            K_z(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = zoriginbottom - (zval + DELTAZ/2.0);
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z_half(k) = d0_z * abscissa_normalized^NPOWER;
            K_z_half(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z_half(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    % zmax edge
    if USE_PML_ZMAX
        abscissa_in_PML = zval - zorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z(k) = d0_z * abscissa_normalized^NPOWER;
            K_z(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
        
        abscissa_in_PML = zval + DELTAZ/2.0 - zorigintop;
        if abscissa_in_PML >= ZERO
            abscissa_normalized = abscissa_in_PML / thickness_PML_z;
            d_z_half(k) = d0_z * abscissa_normalized^NPOWER;
            K_z_half(k) = 1.0 + (K_MAX_PML - 1.0) * abscissa_normalized^NPOWER;
            alpha_z_half(k) = ALPHA_MAX_PML * (1.0 - abscissa_normalized);
        end
    end
    
    b_z(k) = exp(-(d_z(k) / K_z(k) + alpha_z(k)) * DELTAT);
    b_z_half(k) = exp(-(d_z_half(k) / K_z_half(k) + alpha_z_half(k)) * DELTAT);
    
    if abs(d_z(k)) > 1e-6
        a_z(k) = d_z(k) * (b_z(k) - 1.0) / (K_z(k) * (d_z(k) + K_z(k) * alpha_z(k)));
    end
    if abs(d_z_half(k)) > 1e-6
        a_z_half(k) = d_z_half(k) * (b_z_half(k) - 1.0) / (K_z_half(k) * (d_z_half(k) + K_z_half(k) * alpha_z_half(k)));
    end
end

%% Check stability
Courant_number = cp * DELTAT * sqrt(1.0/DELTAX^2 + 1.0/DELTAY^2 + 1.0/DELTAZ^2);
if Courant_number > 1.0
    error('Time step is too large, simulation will be unstable');
end

% Check source indices
fprintf('Source indices: ISOURCE=%d, JSOURCE=%d, KSOURCE=%d\n', ISOURCE, JSOURCE, KSOURCE);
fprintf('Array dimensions: NX=%d, NY=%d, NZ=%d\n', NX, NY, NZ);

if ISOURCE < 1 || ISOURCE > NX || JSOURCE < 1 || JSOURCE > NY || KSOURCE < 1 || KSOURCE > NZ
    error('Source indices are out of bounds');
end

%% Main time loop
%Prepare for GIF creation
filename = 'wavefield_animation.gif';
delay_time = 0.1; % Delay between frames in seconds
frame_count = 0;

% Determine the figure size
fig = figure('Position', [100, 100, 300, 1500]);
rec=zeros(8,NSTEP);
for it = 1:NSTEP
    
    try
    %% Compute stress sigma
    % Update normal stresses (sigmaxx, sigmayy, sigmazz)
    for k = 2:NZ
        for j = 2:NY
            for i = 1:NX-1
                value_dvx_dx = (vx(i+1,j,k) - vx(i,j,k)) * ONE_OVER_DELTAX;
                value_dvy_dy = (vy(i,j,k) - vy(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dvz_dz = (vz(i,j,k) - vz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dvx_dx(i,j,k) = b_x_half(i) * memory_dvx_dx(i,j,k) + a_x_half(i) * value_dvx_dx;
                memory_dvy_dy(i,j,k) = b_y(j) * memory_dvy_dy(i,j,k) + a_y(j) * value_dvy_dy;
                memory_dvz_dz(i,j,k) = b_z(k) * memory_dvz_dz(i,j,k) + a_z(k) * value_dvz_dz;
                
                value_dvx_dx = value_dvx_dx / K_x_half(i) + memory_dvx_dx(i,j,k);
                value_dvy_dy = value_dvy_dy / K_y(j) + memory_dvy_dy(i,j,k);
                value_dvz_dz = value_dvz_dz / K_z(k) + memory_dvz_dz(i,j,k);
                
                sigmaxx(i,j,k) = sigmaxx(i,j,k) + DELTAT_lambdaplus2mu(i,j,k)*value_dvx_dx + DELTAT_lambda(i,j,k)*(value_dvy_dy + value_dvz_dz);
                sigmayy(i,j,k) = sigmayy(i,j,k) + DELTAT_lambda(i,j,k)*(value_dvx_dx + value_dvz_dz) + DELTAT_lambdaplus2mu(i,j,k)*value_dvy_dy;
                sigmazz(i,j,k) = sigmazz(i,j,k) + DELTAT_lambda(i,j,k)*(value_dvx_dx + value_dvy_dy) + DELTAT_lambdaplus2mu(i,j,k)*value_dvz_dz;
            end
        end
    end
    
    % Update sigmaxy
    for k = 1:NZ
        for j = 1:NY-1
            for i = 2:NX
                value_dvy_dx = (vy(i,j,k) - vy(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dvx_dy = (vx(i,j+1,k) - vx(i,j,k)) * ONE_OVER_DELTAY;
                
                memory_dvy_dx(i,j,k) = b_x(i) * memory_dvy_dx(i,j,k) + a_x(i) * value_dvy_dx;
                memory_dvx_dy(i,j,k) = b_y_half(j) * memory_dvx_dy(i,j,k) + a_y_half(j) * value_dvx_dy;
                
                value_dvy_dx = value_dvy_dx / K_x(i) + memory_dvy_dx(i,j,k);
                value_dvx_dy = value_dvx_dy / K_y_half(j) + memory_dvx_dy(i,j,k);
                
                sigmaxy(i,j,k) = sigmaxy(i,j,k) + DELTAT_mu_xy_avg(i,j,k)*(value_dvy_dx + value_dvx_dy);
            end
        end
    end
    
    % Update sigmaxz and sigmayz
    for k = 1:NZ-1
        for j = 1:NY
            for i = 2:NX
                value_dvz_dx = (vz(i,j,k) - vz(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dvx_dz = (vx(i,j,k+1) - vx(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dvz_dx(i,j,k) = b_x(i) * memory_dvz_dx(i,j,k) + a_x(i) * value_dvz_dx;
                memory_dvx_dz(i,j,k) = b_z_half(k) * memory_dvx_dz(i,j,k) + a_z_half(k) * value_dvx_dz;
                
                value_dvz_dx = value_dvz_dx / K_x(i) + memory_dvz_dx(i,j,k);
                value_dvx_dz = value_dvx_dz / K_z_half(k) + memory_dvx_dz(i,j,k);
                
                sigmaxz(i,j,k) = sigmaxz(i,j,k) + DELTAT_mu_xz_avg(i,j,k) *(value_dvz_dx + value_dvx_dz);
            end
        end
        
        for j = 1:NY-1
            for i = 1:NX
                value_dvz_dy = (vz(i,j+1,k) - vz(i,j,k)) * ONE_OVER_DELTAY;
                value_dvy_dz = (vy(i,j,k+1) - vy(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dvz_dy(i,j,k) = b_y_half(j) * memory_dvz_dy(i,j,k) + a_y_half(j) * value_dvz_dy;
                memory_dvy_dz(i,j,k) = b_z_half(k) * memory_dvy_dz(i,j,k) + a_z_half(k) * value_dvy_dz;
                
                value_dvz_dy = value_dvz_dy / K_y_half(j) + memory_dvz_dy(i,j,k);
                value_dvy_dz = value_dvy_dz / K_z_half(k) + memory_dvy_dz(i,j,k);
                
                sigmayz(i,j,k) = sigmayz(i,j,k) + DELTAT_mu_yz_avg(i,j,k) *(value_dvz_dy + value_dvy_dz);
            end
        end
    end
    
    %% Compute velocity
    % Update vx
    for k = 2:NZ
        for j = 2:NY
            for i = 2:NX
                value_dsigmaxx_dx = (sigmaxx(i,j,k) - sigmaxx(i-1,j,k)) * ONE_OVER_DELTAX;
                value_dsigmaxy_dy = (sigmaxy(i,j,k) - sigmaxy(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dsigmaxz_dz = (sigmaxz(i,j,k) - sigmaxz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxx_dx(i,j,k) = b_x(i) * memory_dsigmaxx_dx(i,j,k) + a_x(i) * value_dsigmaxx_dx;
                memory_dsigmaxy_dy(i,j,k) = b_y(j) * memory_dsigmaxy_dy(i,j,k) + a_y(j) * value_dsigmaxy_dy;
                memory_dsigmaxz_dz(i,j,k) = b_z(k) * memory_dsigmaxz_dz(i,j,k) + a_z(k) * value_dsigmaxz_dz;
                
                value_dsigmaxx_dx = value_dsigmaxx_dx / K_x(i) + memory_dsigmaxx_dx(i,j,k);
                value_dsigmaxy_dy = value_dsigmaxy_dy / K_y(j) + memory_dsigmaxy_dy(i,j,k);
                value_dsigmaxz_dz = value_dsigmaxz_dz / K_z(k) + memory_dsigmaxz_dz(i,j,k);
                
                vx(i,j,k) = vx(i,j,k) + DELTAT_over_rhox(i,j,k) *(value_dsigmaxx_dx + value_dsigmaxy_dy + value_dsigmaxz_dz);
            end
        end
    end
    
    % Update vy
    for k = 2:NZ
        for j = 1:NY-1
            for i = 1:NX-1
                value_dsigmaxy_dx = (sigmaxy(i+1,j,k) - sigmaxy(i,j,k)) * ONE_OVER_DELTAX;
                value_dsigmayy_dy = (sigmayy(i,j+1,k) - sigmayy(i,j,k)) * ONE_OVER_DELTAY;
                value_dsigmayz_dz = (sigmayz(i,j,k) - sigmayz(i,j,k-1)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxy_dx(i,j,k) = b_x_half(i) * memory_dsigmaxy_dx(i,j,k) + a_x_half(i) * value_dsigmaxy_dx;
                memory_dsigmayy_dy(i,j,k) = b_y_half(j) * memory_dsigmayy_dy(i,j,k) + a_y_half(j) * value_dsigmayy_dy;
                memory_dsigmayz_dz(i,j,k) = b_z(k) * memory_dsigmayz_dz(i,j,k) + a_z(k) * value_dsigmayz_dz;
                
                value_dsigmaxy_dx = value_dsigmaxy_dx / K_x_half(i) + memory_dsigmaxy_dx(i,j,k);
                value_dsigmayy_dy = value_dsigmayy_dy / K_y_half(j) + memory_dsigmayy_dy(i,j,k);
                value_dsigmayz_dz = value_dsigmayz_dz / K_z(k) + memory_dsigmayz_dz(i,j,k);
                
                vy(i,j,k) = vy(i,j,k) + DELTAT_over_rhoy(i,j,k)*(value_dsigmaxy_dx + value_dsigmayy_dy + value_dsigmayz_dz);
            end
        end
    end
    
    % Update vz
    for k = 1:NZ-1
        for j = 2:NY
            for i = 1:NX-1
                value_dsigmaxz_dx = (sigmaxz(i+1,j,k) - sigmaxz(i,j,k)) * ONE_OVER_DELTAX;
                value_dsigmayz_dy = (sigmayz(i,j,k) - sigmayz(i,j-1,k)) * ONE_OVER_DELTAY;
                value_dsigmazz_dz = (sigmazz(i,j,k+1) - sigmazz(i,j,k)) * ONE_OVER_DELTAZ;
                
                memory_dsigmaxz_dx(i,j,k) = b_x_half(i) * memory_dsigmaxz_dx(i,j,k) + a_x_half(i) * value_dsigmaxz_dx;
                memory_dsigmayz_dy(i,j,k) = b_y(j) * memory_dsigmayz_dy(i,j,k) + a_y(j) * value_dsigmayz_dy;
                memory_dsigmazz_dz(i,j,k) = b_z_half(k) * memory_dsigmazz_dz(i,j,k) + a_z_half(k) * value_dsigmazz_dz;
                
                value_dsigmaxz_dx = value_dsigmaxz_dx / K_x_half(i) + memory_dsigmaxz_dx(i,j,k);
                value_dsigmayz_dy = value_dsigmayz_dy / K_y(j) + memory_dsigmayz_dy(i,j,k);
                value_dsigmazz_dz = value_dsigmazz_dz / K_z_half(k) + memory_dsigmazz_dz(i,j,k);
                
                vz(i,j,k) = vz(i,j,k) + DELTAT_over_rhoz(i,j,k)*(value_dsigmaxz_dx + value_dsigmayz_dy + value_dsigmazz_dz);
            end
        end
    end
    
    %% Add source
    a = PI*PI*f0*f0;
    t = (it-1)*DELTAT;
    
    % First derivative of a Gaussian
    source_term = -factor * 2.0*a*(t-t0)*exp(-a*(t-t0)^2);
    
    force_x = source_term;
    
    vx(ISOURCE, JSOURCE, KSOURCE) = vx(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
    vx(ISOURCE-1, JSOURCE, KSOURCE) = vx(ISOURCE-1, JSOURCE, KSOURCE) - force_x * DELTAT / rho;    
%     sigmayy(ISOURCE, JSOURCE, KSOURCE) = sigmayy(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
%     sigmazz(ISOURCE, JSOURCE, KSOURCE) = sigmazz(ISOURCE, JSOURCE, KSOURCE) + force_x * DELTAT / rho;
    %% Apply boundary conditions (Dirichlet)
    % xmin and xmax
    vx(1,:,:) = ZERO;
    vy(1,:,:) = ZERO;
    vz(1,:,:) = ZERO;
    vx(NX,:,:) = ZERO;
    vy(NX,:,:) = ZERO;
    vz(NX,:,:) = ZERO;
    
    % ymin and ymax
    vx(:,1,:) = ZERO;
    vy(:,1,:) = ZERO;
    vz(:,1,:) = ZERO;
    vx(:,NY,:) = ZERO;
    vy(:,NY,:) = ZERO;
    vz(:,NY,:) = ZERO;
    
    % zmin and zmax
    vx(:,:,1) = ZERO;
    vy(:,:,1) = ZERO;
    vz(:,:,1) = ZERO;
    vx(:,:,NZ) = ZERO;
    vy(:,:,NZ) = ZERO;
    vz(:,:,NZ) = ZERO;
    
    catch ME
   fprintf('Error at time step %d\n', it);
   fprintf('Error message: %s\n', ME.message);
   fprintf('Error identifier: %s\n', ME.identifier);
   rethrow(ME);
    end
  fprintf('time: %.4f seconds\n', it);  
    if mod(it, 10) == 0
        % Create the plot
%         plot_3d_slices(sigmaxx+sigmayy+sigmazz, 0.5, 0.5, 0.25)
        pcolor(squeeze(sigmaxx(ISOURCE, :, :))');
%         caxis([-1e1 1e1]);
        shading interp;
        grid off;
        colorbar;
        title(sprintf('Wavefield at Time Step %d (%.3f s)', it, it*DELTAT));
        xlabel('Y Index');
        ylabel('Z Index');
        
        % Capture the frame
        frame = getframe(fig);
        im = frame2im(frame);
        [imind, cm] = rgb2ind(im, 256);
        
        % Write to the GIF file
        if frame_count == 0
            imwrite(imind, cm, filename, 'gif', 'Loopcount', inf, 'DelayTime', delay_time);
        else
            imwrite(imind, cm, filename, 'gif', 'WriteMode', 'append', 'DelayTime', delay_time);
        end
        
        frame_count = frame_count + 1;
        drawnow;
    end
  rec(1,it)=vy(ISOURCE,JSOURCE,KSOURCE+150);
  rec(2,it)=vy(ISOURCE,JSOURCE,KSOURCE+157);
  rec(3,it)=vy(ISOURCE,JSOURCE,KSOURCE+164);
  rec(4,it)=vy(ISOURCE,JSOURCE,KSOURCE+171);
  rec(5,it)=vy(ISOURCE,JSOURCE,KSOURCE+178);
  rec(6,it)=vy(ISOURCE,JSOURCE,KSOURCE+185);
  rec(7,it)=vy(ISOURCE,JSOURCE,KSOURCE+192);
  rec(8,it)=vy(ISOURCE,JSOURCE,KSOURCE+199);
end

fprintf('Simulation completed successfully!\n');
fprintf('Final time: %.4f seconds\n', (NSTEP-1)*DELTAT);

